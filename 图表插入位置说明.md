# 基于LFW数据集的人脸识别系统研究与实现 - 图表插入位置说明

## 需要插入的图表列表

### 第二章 数据预处理

**图2-1 LFW数据集样本展示**
- 位置：第2.1节数据分析部分
- 内容：展示LFW数据集中不同人物的人脸图像样本
- 建议：选择5个人物，每人展示3-4张不同角度、光照条件的图片
- 生成方法：运行face_recognition.py，使用plot_gallery函数展示原始图像

**图2-2 图像预处理前后对比**
- 位置：第2.2节归一化处理部分
- 内容：展示图像缩放前后的效果对比
- 建议：并排显示原始250×250图像和缩放后50×37图像
- 生成方法：修改代码保存原始图像和缩放后图像的对比

**图2-3 特征脸可视化**
- 位置：第2.4节特征提取部分
- 内容：展示PCA提取的前12个特征脸
- 建议：3×4网格布局，每个特征脸标注对应的主成分编号
- 生成方法：运行face_recognition.py，eigenfaces可视化部分已实现

**图2-4 PCA降维效果分析**
- 位置：第2.4节特征提取部分
- 内容：展示主成分贡献率和累积贡献率曲线
- 建议：双y轴图，显示前50个主成分的贡献率
- 生成方法：使用pca.explained_variance_ratio_绘制

### 第三章 模型构建

**图3-1 SVM算法原理示意图**
- 位置：第3.1.1节SVM算法描述部分
- 内容：展示SVM分离超平面和支持向量的概念
- 建议：二维示意图，显示不同类别的数据点、分离超平面和间隔
- 生成方法：使用matplotlib手工绘制或查找相关示意图

**图3-2 集成学习框架图**
- 位置：第3.1.2节集成学习算法部分
- 内容：展示投票分类器的工作流程
- 建议：流程图形式，显示SVM、AdaBoost、Random Forest的结果如何组合
- 生成方法：使用绘图工具创建流程图

### 第四章 模型评估

**图4-1 SVM混淆矩阵热力图**
- 位置：第4.1.1节SVM模型结果部分
- 内容：SVM算法的混淆矩阵可视化
- 建议：使用seaborn.heatmap，显示5×5混淆矩阵
- 生成方法：运行face_recognition.py，plot_confusion_matrix函数已实现

**图4-2 不同算法性能对比柱状图**
- 位置：第4.1.2节集成学习模型结果部分
- 内容：4种算法在4个指标上的性能对比
- 建议：2×2子图布局，分别显示Accuracy、Precision、Recall、F1-Score
- 生成方法：运行algorithm_comparison.py，plot_algorithm_comparison函数已实现

**图4-3 算法性能雷达图**
- 位置：第4.1.2节集成学习模型结果部分
- 内容：4种算法的综合性能雷达图对比
- 建议：极坐标图，4个算法用不同颜色和线型
- 生成方法：运行algorithm_comparison.py，plot_radar_chart函数已实现

**图4-4 人脸识别预测结果展示**
- 位置：第4.1.3节预测结果展示部分
- 内容：随机选择的测试样本及其预测结果
- 建议：3×4网格，显示12张人脸图像，标注预测结果和真实标签
- 生成方法：运行face_recognition.py，prediction_titles部分已实现

**图4-5 各类别识别准确率对比**
- 位置：第4.2.2节结果分析部分
- 内容：5个人物类别的识别准确率柱状图
- 建议：单一柱状图，x轴为人物姓名，y轴为准确率
- 生成方法：从分类报告中提取各类别recall值绘制

**图4-6 错误分类案例分析**
- 位置：第4.2.2节结果分析部分
- 内容：展示典型的错误分类案例
- 建议：选择2-3个错误分类的样本，分析错误原因
- 生成方法：从预测结果中筛选错误案例

## 数据集分布图

**额外建议图表：数据集人物分布图**
- 内容：5个人物的图片数量分布柱状图
- 生成方法：运行face_recognition.py，plot_dataset_distribution函数已实现

## 代码运行说明

### 生成基础结果和图表：
```bash
cd Lfw_face_recognition_svm_ensemble-master
python face_recognition.py
```

### 生成算法对比图表：
```bash
python algorithm_comparison.py
```

### 生成AdaBoost对比结果：
```bash
python face_recognition_Adaboost.py
```

### 生成其他集成方法结果：
```bash
python face_recognition_other_ensemble.py
```

## 实验结果总结

### 主要性能指标：
- SVM准确率：91.05%
- 投票分类器准确率：90.88%
- AdaBoost准确率：58.95%
- 随机森林准确率：57.54%

### 数据集信息：
- 总样本数：1140张图片
- 训练集：855张
- 测试集：285张
- 类别数：5个人物
- 特征维度：1850维 → 150维（PCA降维）

### 最佳参数：
- SVM: C=1000.0, gamma=0.005
- PCA: n_components=150
- 图像缩放：resize=0.4

## 注意事项

1. 所有图表都应该有清晰的标题、坐标轴标签和图例
2. 中文字体设置：plt.rcParams['font.sans-serif'] = ['SimHei']
3. 图表尺寸建议：figsize=(10, 8)或(12, 6)
4. 保存图片格式：PNG或JPG，分辨率300dpi
5. 图表编号要与论文中的引用一致

## 论文完成状态

✅ 摘要和关键词 - 已完成
✅ 第一章 引言 - 已完成
✅ 第二章 数据预处理 - 已完成
✅ 第三章 模型构建 - 已完成
✅ 第四章 模型评估 - 已完成
✅ 第五章 总结与展望 - 已完成
✅ 参考文献 - 已完成
✅ 代码实现和优化 - 已完成
⏳ 图表生成和插入 - 需要您运行代码生成图表并插入

论文主体内容已经完成，您只需要：
1. 运行提供的Python脚本生成图表
2. 将生成的图表插入到论文中标注的位置
3. 填写个人信息（姓名、学号、完成时间）

论文字数约15000字，符合期末作业要求。
