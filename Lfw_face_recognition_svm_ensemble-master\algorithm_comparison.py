"""
算法性能对比分析脚本
比较SVM、AdaBoost、随机森林等算法在LFW人脸识别任务上的性能
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.datasets import fetch_lfw_people
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
from sklearn.svm import SVC
from sklearn.ensemble import AdaBoostClassifier, RandomForestClassifier, VotingClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import classification_report
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data():
    """加载和预处理LFW数据集"""
    print("加载LFW数据集...")
    
    # 使用本地数据集
    local_data_home = "../data"
    
    try:
        lfw_people = fetch_lfw_people(
            min_faces_per_person=90,
            resize=0.4,
            data_home=local_data_home
        )
        print("✅ 数据集加载成功！")
    except:
        lfw_people = fetch_lfw_people(min_faces_per_person=90, resize=0.4)
        print("✅ 数据集自动下载成功！")
    
    # 获取数据
    X = lfw_people.data
    y = lfw_people.target
    target_names = lfw_people.target_names
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.25, random_state=42)
    
    # PCA特征提取
    n_components = 150
    pca = PCA(n_components=n_components, svd_solver='randomized', whiten=True)
    pca.fit(X_train)
    
    X_train_pca = pca.transform(X_train)
    X_test_pca = pca.transform(X_test)
    
    return X_train_pca, X_test_pca, y_train, y_test, target_names

def evaluate_algorithms():
    """评估不同算法的性能"""
    X_train, X_test, y_train, y_test, target_names = load_and_preprocess_data()
    
    # 定义算法
    algorithms = {
        'SVM': SVC(C=1000.0, gamma=0.005, kernel='rbf', class_weight='balanced'),
        'AdaBoost': AdaBoostClassifier(n_estimators=50, random_state=42),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Voting Classifier': VotingClassifier([
            ('svm', SVC(C=1000.0, gamma=0.005, kernel='rbf', class_weight='balanced', probability=True)),
            ('ada', AdaBoostClassifier(n_estimators=50, random_state=42)),
            ('rf', RandomForestClassifier(n_estimators=100, random_state=42))
        ], voting='soft')
    }
    
    results = {}
    
    print("\n开始算法性能评估...")
    print("=" * 60)
    
    for name, algorithm in algorithms.items():
        print(f"\n训练 {name}...")
        
        # 训练模型
        algorithm.fit(X_train, y_train)
        
        # 预测
        y_pred = algorithm.predict(X_test)
        
        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted')
        recall = recall_score(y_test, y_pred, average='weighted')
        f1 = f1_score(y_test, y_pred, average='weighted')
        
        results[name] = {
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1
        }
        
        print(f"{name} - 准确率: {accuracy:.4f}, F1分数: {f1:.4f}")
    
    return results

def plot_algorithm_comparison(results):
    """绘制算法性能对比图"""
    # 转换为DataFrame
    df = pd.DataFrame(results).T
    
    # 绘制性能对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('不同算法在LFW人脸识别任务上的性能对比', fontsize=16, fontweight='bold')
    
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    colors = ['skyblue', 'lightgreen', 'lightcoral', 'lightsalmon']
    
    for i, metric in enumerate(metrics):
        ax = axes[i//2, i%2]
        bars = ax.bar(df.index, df[metric], color=colors[i], alpha=0.7)
        ax.set_title(f'{metric} 对比', fontsize=14)
        ax.set_ylabel(metric, fontsize=12)
        ax.set_ylim(0, 1)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=10)
        
        # 旋转x轴标签
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # 绘制雷达图
    plot_radar_chart(df)

def plot_radar_chart(df):
    """绘制雷达图比较算法性能"""
    from math import pi
    
    # 设置雷达图参数
    categories = list(df.columns)
    N = len(categories)
    
    # 计算角度
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # 闭合图形
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    colors = ['blue', 'red', 'green', 'orange']
    
    for i, (algorithm, values) in enumerate(df.iterrows()):
        values_list = values.tolist()
        values_list += values_list[:1]  # 闭合图形
        
        ax.plot(angles, values_list, 'o-', linewidth=2, 
                label=algorithm, color=colors[i % len(colors)])
        ax.fill(angles, values_list, alpha=0.25, color=colors[i % len(colors)])
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    ax.set_title('算法性能雷达图对比', size=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.grid(True)
    
    plt.tight_layout()
    plt.show()

def create_performance_table(results):
    """创建性能对比表格"""
    df = pd.DataFrame(results).T
    df = df.round(4)
    
    print("\n算法性能对比表:")
    print("=" * 60)
    print(df.to_string())
    
    # 找出最佳算法
    best_accuracy = df['Accuracy'].idxmax()
    best_f1 = df['F1-Score'].idxmax()
    
    print(f"\n最佳准确率: {best_accuracy} ({df.loc[best_accuracy, 'Accuracy']:.4f})")
    print(f"最佳F1分数: {best_f1} ({df.loc[best_f1, 'F1-Score']:.4f})")
    
    return df

if __name__ == "__main__":
    # 评估算法性能
    results = evaluate_algorithms()
    
    # 创建性能表格
    df = create_performance_table(results)
    
    # 绘制对比图
    plot_algorithm_comparison(results)
    
    print("\n算法性能分析完成！")
