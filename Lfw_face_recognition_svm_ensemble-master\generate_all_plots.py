"""
生成论文所需的所有图表
包括数据集分析、特征提取、模型性能等各种可视化图表
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from sklearn.datasets import fetch_lfw_people
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
from sklearn.svm import SVC
from sklearn.ensemble import AdaBoostClassifier, RandomForestClassifier, VotingClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import GridSearchCV
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载和预处理数据"""
    print("加载LFW数据集...")
    
    local_data_home = "../data"
    try:
        lfw_people = fetch_lfw_people(
            min_faces_per_person=90,
            resize=0.4,
            data_home=local_data_home
        )
        print("✅ 数据集加载成功！")
    except:
        lfw_people = fetch_lfw_people(min_faces_per_person=90, resize=0.4)
        print("✅ 数据集自动下载成功！")
    
    return lfw_people

def plot_dataset_samples(lfw_people):
    """图2-1: LFW数据集样本展示"""
    X = lfw_people.data
    y = lfw_people.target
    target_names = lfw_people.target_names
    h, w = lfw_people.images.shape[1], lfw_people.images.shape[2]
    
    plt.figure(figsize=(15, 10))
    plt.suptitle('图2-1 LFW数据集样本展示', fontsize=16, fontweight='bold')
    
    # 为每个人物选择4张图片
    for i, name in enumerate(target_names):
        indices = np.where(y == i)[0][:4]  # 选择前4张图片
        
        for j, idx in enumerate(indices):
            plt.subplot(len(target_names), 4, i*4 + j + 1)
            plt.imshow(lfw_people.images[idx], cmap='gray')
            if j == 0:
                plt.ylabel(name.split()[-1], fontsize=12)
            plt.title(f'样本 {j+1}', fontsize=10)
            plt.xticks([])
            plt.yticks([])
    
    plt.tight_layout()
    plt.savefig('图2-1_LFW数据集样本展示.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_pca_analysis(X_train, pca):
    """图2-4: PCA降维效果分析"""
    # 计算累积贡献率
    explained_variance_ratio = pca.explained_variance_ratio_
    cumulative_variance_ratio = np.cumsum(explained_variance_ratio)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 主成分贡献率
    ax1.bar(range(1, 51), explained_variance_ratio[:50], alpha=0.7, color='skyblue')
    ax1.set_xlabel('主成分编号', fontsize=12)
    ax1.set_ylabel('贡献率', fontsize=12)
    ax1.set_title('前50个主成分的贡献率', fontsize=14)
    ax1.grid(True, alpha=0.3)
    
    # 累积贡献率
    ax2.plot(range(1, 151), cumulative_variance_ratio, 'ro-', linewidth=2, markersize=4)
    ax2.axhline(y=0.95, color='red', linestyle='--', alpha=0.7, label='95%阈值')
    ax2.set_xlabel('主成分数量', fontsize=12)
    ax2.set_ylabel('累积贡献率', fontsize=12)
    ax2.set_title('主成分累积贡献率曲线', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.suptitle('图2-4 PCA降维效果分析', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('图2-4_PCA降维效果分析.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_eigenfaces(pca, h, w):
    """图2-3: 特征脸可视化"""
    eigenfaces = pca.components_.reshape((pca.n_components, h, w))
    
    plt.figure(figsize=(12, 9))
    plt.suptitle('图2-3 特征脸可视化（前12个主成分）', fontsize=16, fontweight='bold')
    
    for i in range(12):
        plt.subplot(3, 4, i + 1)
        plt.imshow(eigenfaces[i], cmap='gray')
        plt.title(f'特征脸 {i+1}', fontsize=12)
        plt.xticks([])
        plt.yticks([])
    
    plt.tight_layout()
    plt.savefig('图2-3_特征脸可视化.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_confusion_matrix_heatmap(y_test, y_pred, target_names):
    """图4-1: SVM混淆矩阵热力图"""
    cm = confusion_matrix(y_test, y_pred)
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=[name.split()[-1] for name in target_names],
                yticklabels=[name.split()[-1] for name in target_names])
    plt.title('图4-1 SVM混淆矩阵热力图', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.tight_layout()
    plt.savefig('图4-1_SVM混淆矩阵热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_class_accuracy(y_test, y_pred, target_names):
    """图4-5: 各类别识别准确率对比"""
    from sklearn.metrics import classification_report
    
    # 获取分类报告
    report = classification_report(y_test, y_pred, target_names=target_names, output_dict=True)
    
    # 提取各类别的recall（识别准确率）
    class_names = [name.split()[-1] for name in target_names]
    accuracies = [report[name]['recall'] for name in target_names]
    
    plt.figure(figsize=(12, 6))
    bars = plt.bar(class_names, accuracies, color='lightcoral', alpha=0.7)
    plt.xlabel('人物类别', fontsize=12)
    plt.ylabel('识别准确率', fontsize=12)
    plt.title('图4-5 各类别识别准确率对比', fontsize=16, fontweight='bold')
    plt.ylim(0, 1)
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontsize=11)
    
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图4-5_各类别识别准确率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_prediction_results(X_test, y_test, y_pred, target_names, h, w):
    """图4-4: 人脸识别预测结果展示"""
    plt.figure(figsize=(15, 12))
    plt.suptitle('图4-4 人脸识别预测结果展示', fontsize=16, fontweight='bold')
    
    # 选择前12个测试样本
    for i in range(12):
        plt.subplot(3, 4, i + 1)
        plt.imshow(X_test[i].reshape(h, w), cmap='gray')
        
        pred_name = target_names[y_pred[i]].split()[-1]
        true_name = target_names[y_test[i]].split()[-1]
        
        # 根据预测是否正确设置颜色
        color = 'green' if y_pred[i] == y_test[i] else 'red'
        plt.title(f'预测: {pred_name}\n真实: {true_name}', 
                 fontsize=10, color=color, fontweight='bold')
        plt.xticks([])
        plt.yticks([])
    
    plt.tight_layout()
    plt.savefig('图4-4_人脸识别预测结果展示.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_dataset_distribution(y, target_names):
    """数据集人物分布图"""
    counts = [np.sum(y == i) for i in range(len(target_names))]
    class_names = [name.split()[-1] for name in target_names]
    
    plt.figure(figsize=(12, 6))
    bars = plt.bar(class_names, counts, color='skyblue', alpha=0.7)
    plt.xlabel('人物', fontsize=12)
    plt.ylabel('图片数量', fontsize=12)
    plt.title('LFW数据集中各人物图片数量分布', fontsize=14, fontweight='bold')
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 5,
                f'{count}', ha='center', va='bottom', fontsize=11)
    
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('数据集人物分布图.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数：生成所有图表"""
    print("开始生成论文所需的所有图表...")
    
    # 加载数据
    lfw_people = load_data()
    X = lfw_people.data
    y = lfw_people.target
    target_names = lfw_people.target_names
    h, w = lfw_people.images.shape[1], lfw_people.images.shape[2]
    
    print(f"数据集信息: {X.shape[0]}个样本, {len(target_names)}个类别")
    
    # 生成图2-1: 数据集样本展示
    print("生成图2-1: LFW数据集样本展示...")
    plot_dataset_samples(lfw_people)
    
    # 生成数据集分布图
    print("生成数据集人物分布图...")
    plot_dataset_distribution(y, target_names)
    
    # 数据预处理
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.25, random_state=42)
    
    # PCA特征提取
    print("进行PCA特征提取...")
    n_components = 150
    pca = PCA(n_components=n_components, svd_solver='randomized', whiten=True)
    pca.fit(X_train)
    X_train_pca = pca.transform(X_train)
    X_test_pca = pca.transform(X_test)
    
    # 生成图2-3: 特征脸可视化
    print("生成图2-3: 特征脸可视化...")
    plot_eigenfaces(pca, h, w)
    
    # 生成图2-4: PCA降维效果分析
    print("生成图2-4: PCA降维效果分析...")
    plot_pca_analysis(X_train, pca)
    
    # 训练SVM模型
    print("训练SVM模型...")
    svm_clf = SVC(C=1000.0, gamma=0.005, kernel='rbf', class_weight='balanced')
    svm_clf.fit(X_train_pca, y_train)
    y_pred = svm_clf.predict(X_test_pca)
    
    # 生成图4-1: SVM混淆矩阵热力图
    print("生成图4-1: SVM混淆矩阵热力图...")
    plot_confusion_matrix_heatmap(y_test, y_pred, target_names)
    
    # 生成图4-4: 预测结果展示
    print("生成图4-4: 人脸识别预测结果展示...")
    plot_prediction_results(X_test, y_test, y_pred, target_names, h, w)
    
    # 生成图4-5: 各类别识别准确率对比
    print("生成图4-5: 各类别识别准确率对比...")
    plot_class_accuracy(y_test, y_pred, target_names)
    
    print("所有图表生成完成！")
    print("请运行 algorithm_comparison.py 生成算法对比图表")

if __name__ == "__main__":
    main()
