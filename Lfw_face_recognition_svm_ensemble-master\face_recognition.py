"""
===================================================
基于LFW数据集的人脸识别系统 - SVM实现
===================================================

本程序实现了基于Labeled Faces in the Wild (LFW)数据集的人脸识别系统
使用主成分分析(PCA)进行特征提取，支持向量机(SVM)进行分类

数据集来源: http://vis-www.cs.umass.edu/lfw/lfw-funneled.tgz (233MB)
LFW官网: http://vis-www.cs.umass.edu/lfw/

主要功能:
1. 数据加载和预处理
2. PCA特征提取(特征脸)
3. SVM分类器训练和优化
4. 模型性能评估
5. 结果可视化

作者: [您的姓名]
日期: [当前日期]
课程: 非结构化数据挖掘
"""
from __future__ import print_function

# 导入必要的库
from time import time
import logging
import matplotlib.pyplot as plt
import numpy as np
import os

# 导入sklearn相关模块
from sklearn.model_selection import train_test_split
from sklearn.model_selection import GridSearchCV
from sklearn.datasets import fetch_lfw_people
from sklearn.metrics import classification_report
from sklearn.metrics import confusion_matrix
from sklearn.decomposition import PCA
from sklearn.svm import SVC

print(__doc__)

# 配置日志输出格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(message)s')

###############################################################################
# 第一步: 数据加载和基本信息统计
###############################################################################

print("=" * 60)
print("第一步: 加载LFW数据集")
print("=" * 60)

# 加载LFW数据集
# min_faces_per_person: 每个人最少的图片数量，用于过滤样本数过少的人物
# resize: 图像缩放比例，减小图像尺寸以提高计算效率

# 使用本地数据集
local_data_home = "./data"

try:
    # 首先尝试从本地加载
    if os.path.exists(local_data_home):
        print(f"📁 使用本地数据集: {local_data_home}")
        lfw_people = fetch_lfw_people(
            min_faces_per_person=90,
            resize=0.4,
            data_home=local_data_home
        )
        print("✅ 本地数据集加载成功！")
    else:
        # 如果本地没有数据，使用自动下载
        print("⚠️  本地数据集不存在，尝试自动下载...")
        lfw_people = fetch_lfw_people(min_faces_per_person=90, resize=0.4)
        print("✅ 数据集自动下载成功！")

except Exception as e:
    print(f"❌ 数据集加载失败: {e}")
    print("请检查数据集是否正确放置在 './data' 文件夹中")
    raise

# 获取图像数组的维度信息(用于后续绘图)
n_samples, h, w = lfw_people.images.shape

# 获取特征数据(将图像展平为一维向量)
# 机器学习算法使用展平后的像素数据，忽略像素的相对位置信息
X = lfw_people.data
n_features = X.shape[1]

# 获取标签数据(人物ID)
y = lfw_people.target
target_names = lfw_people.target_names  # 人物姓名
n_classes = target_names.shape[0]       # 类别数量

# 打印数据集基本信息
print("数据集基本信息:")
print(f"样本总数: {n_samples}")
print(f"特征维度: {n_features}")
print(f"类别数量: {n_classes}")
print(f"图像尺寸: {h} x {w}")
print(f"标签形状: {y.shape}")
print("\n包含的人物:")
for i, name in enumerate(target_names):
    count = np.sum(y == i)
    print(f"{i+1:2d}. {name}: {count} 张图片")

# 数据集可视化
print("\n📊 生成数据集可视化...")
plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

# 1. 显示数据集样本
fig, axes = plt.subplots(2, 6, figsize=(15, 6))
fig.suptitle('LFW数据集样本展示', fontsize=16)
for i in range(12):
    row = i // 6
    col = i % 6
    axes[row, col].imshow(lfw_people.images[i], cmap='gray')
    axes[row, col].set_title(f'{target_names[y[i]].split()[-1]}', fontsize=10)
    axes[row, col].axis('off')
plt.tight_layout()
plt.savefig('数据集样本展示.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. 类别分布统计
plt.figure(figsize=(12, 6))
names = [name.split()[-1] for name in target_names]
counts = [np.sum(y == i) for i in range(n_classes)]
bars = plt.bar(names, counts, color='skyblue', edgecolor='navy', alpha=0.7)
plt.title('各人物图片数量分布', fontsize=16)
plt.xlabel('人物姓名', fontsize=12)
plt.ylabel('图片数量', fontsize=12)
plt.xticks(rotation=45)

# 在柱状图上添加数值标签
for bar, count in zip(bars, counts):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
             str(count), ha='center', va='bottom', fontsize=10)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('类别分布统计.png', dpi=300, bbox_inches='tight')
plt.show()


###############################################################################
# 第二步: 数据集划分
###############################################################################

print("\n" + "=" * 60)
print("第二步: 划分训练集和测试集")
print("=" * 60)

# 使用分层抽样将数据集划分为训练集和测试集
# test_size=0.25: 25%的数据用作测试集
# random_state=42: 设置随机种子，确保结果可重现
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.25, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")
print(f"训练标签形状: {y_train.shape}")
print(f"测试标签形状: {y_test.shape}")

###############################################################################
# 第三步: PCA特征提取(特征脸)
###############################################################################

print("\n" + "=" * 60)
print("第三步: PCA特征提取")
print("=" * 60)

# 设置主成分数量
n_components = 150

print(f"从 {X_train.shape[0]} 张人脸图像中提取前 {n_components} 个特征脸...")
t0 = time()

# 创建PCA对象并训练
# n_components: 保留的主成分数量
# svd_solver='randomized': 使用随机化SVD算法，适合大数据集
# whiten=True: 白化处理，使特征具有单位方差
pca = PCA(n_components=n_components, svd_solver='randomized', whiten=True)
pca.fit(X_train)

print(f"PCA训练完成，耗时: {time() - t0:.3f}秒")

eigenfaces = pca.components_.reshape((n_components, h, w))

print("Projecting the input data on the eigenfaces orthonormal basis")
t0 = time()
X_train_pca = pca.transform(X_train)
X_test_pca = pca.transform(X_test)
print("done in %0.3fs" % (time() - t0))

print("shape of xtrain_pca")
print(X_train_pca.shape)

# PCA结果可视化
print("\n📊 生成PCA分析可视化...")

# 1. 解释方差比例图
plt.figure(figsize=(12, 5))

# 左图：前20个主成分的解释方差比例
plt.subplot(1, 2, 1)
plt.plot(range(1, 21), pca.explained_variance_ratio_[:20], 'bo-', linewidth=2, markersize=6)
plt.title('前20个主成分的解释方差比例', fontsize=14)
plt.xlabel('主成分编号', fontsize=12)
plt.ylabel('解释方差比例', fontsize=12)
plt.grid(True, alpha=0.3)

# 右图：累积解释方差比例
plt.subplot(1, 2, 2)
cumsum_ratio = np.cumsum(pca.explained_variance_ratio_)
plt.plot(range(1, len(cumsum_ratio)+1), cumsum_ratio, 'ro-', linewidth=2, markersize=4)
plt.title(f'累积解释方差比例 (前{n_components}个主成分)', fontsize=14)
plt.xlabel('主成分数量', fontsize=12)
plt.ylabel('累积解释方差比例', fontsize=12)
plt.axhline(y=0.95, color='g', linestyle='--', alpha=0.7, label='95%阈值')
plt.axhline(y=0.99, color='r', linestyle='--', alpha=0.7, label='99%阈值')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('PCA解释方差分析.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"📈 前{n_components}个主成分累积解释方差比例: {cumsum_ratio[-1]:.4f}")
print(f"📈 达到95%解释方差需要的主成分数量: {np.argmax(cumsum_ratio >= 0.95) + 1}")
print(f"📈 达到99%解释方差需要的主成分数量: {np.argmax(cumsum_ratio >= 0.99) + 1}")

###############################################################################
# 第四步: SVM分类器训练和参数优化
###############################################################################

print("\n" + "=" * 60)
print("第四步: SVM分类器训练")
print("=" * 60)

print("🔧 开始训练SVM分类器...")
t0 = time()

# 定义参数网格进行网格搜索
# C: 正则化参数，控制模型复杂度
# gamma: RBF核函数参数，控制单个训练样本的影响范围
param_grid = {
    'C': [1e3, 5e3, 1e4, 5e4, 1e5],
    'gamma': [0.0001, 0.0005, 0.001, 0.005, 0.01, 0.1]
}

# 使用网格搜索找到最佳参数组合
# kernel='rbf': 使用径向基函数核
# class_weight='balanced': 自动调整权重以处理类别不平衡
clf = GridSearchCV(
    SVC(kernel='rbf', class_weight='balanced'),
    param_grid,
    cv=3,  # 3折交叉验证
    scoring='accuracy',
    n_jobs=-1  # 使用所有CPU核心
)

clf = clf.fit(X_train_pca, y_train)
print(f"✅ SVM训练完成，耗时: {time() - t0:.3f}秒")
print(f"🎯 最佳参数组合: {clf.best_estimator_}")
print(f"📊 最佳交叉验证得分: {clf.best_score_:.4f}")

###############################################################################
# 第五步: 模型性能评估
###############################################################################

print("\n" + "=" * 60)
print("第五步: 模型性能评估")
print("=" * 60)

print("🔮 在测试集上进行预测...")
t0 = time()
y_pred = clf.predict(X_test_pca)
print(f"✅ 预测完成，耗时: {time() - t0:.3f}秒")

# 计算准确率
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, average='weighted')
recall = recall_score(y_test, y_pred, average='weighted')
f1 = f1_score(y_test, y_pred, average='weighted')

print(f"\n📈 模型性能指标:")
print(f"准确率 (Accuracy): {accuracy:.4f}")
print(f"精确率 (Precision): {precision:.4f}")
print(f"召回率 (Recall): {recall:.4f}")
print(f"F1分数 (F1-Score): {f1:.4f}")

print(f"\n📋 详细分类报告:")
print(classification_report(y_test, y_pred, target_names=target_names))

print(f"\n🔢 混淆矩阵:")
cm = confusion_matrix(y_test, y_pred, labels=range(n_classes))
print(cm)


###############################################################################
# 第六步: 结果可视化
###############################################################################

print("\n" + "=" * 60)
print("第六步: 结果可视化")
print("=" * 60)

import seaborn as sns
plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

def plot_gallery(images, titles, h, w, n_row=3, n_col=4, title_text="图像展示"):
    """绘制图像画廊的辅助函数"""
    plt.figure(figsize=(1.8 * n_col, 2.4 * n_row))
    plt.suptitle(title_text, fontsize=16, y=0.98)
    plt.subplots_adjust(bottom=0, left=.01, right=.99, top=.90, hspace=.35)
    for i in range(min(n_row * n_col, len(images))):
        plt.subplot(n_row, n_col, i + 1)
        plt.imshow(images[i].reshape((h, w)), cmap=plt.cm.gray)
        plt.title(titles[i], size=10)
        plt.xticks(())
        plt.yticks(())

def title(y_pred, y_test, target_names, i):
    """生成预测结果标题"""
    pred_name = target_names[y_pred[i]].rsplit(' ', 1)[-1]
    true_name = target_names[y_test[i]].rsplit(' ', 1)[-1]
    return f'预测: {pred_name}\n真实: {true_name}'

# 1. 绘制预测结果对比
print("📊 生成预测结果可视化...")
prediction_titles = [title(y_pred, y_test, target_names, i)
                     for i in range(min(12, y_pred.shape[0]))]
plot_gallery(X_test[:12], prediction_titles, h, w, title_text="人脸识别预测结果对比")
plt.savefig('人脸识别预测结果.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. 绘制特征脸(Eigenfaces)
print("📊 生成特征脸可视化...")
eigenface_titles = [f"特征脸 {i+1}" for i in range(min(12, eigenfaces.shape[0]))]
plot_gallery(eigenfaces[:12], eigenface_titles, h, w, title_text="主要特征脸(Eigenfaces)")
plt.savefig('特征脸可视化.png', dpi=300, bbox_inches='tight')
plt.show()

# 3. 绘制混淆矩阵热力图
print("📊 生成混淆矩阵热力图...")
plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=[name.split()[-1] for name in target_names],
            yticklabels=[name.split()[-1] for name in target_names])
plt.title('混淆矩阵热力图', fontsize=16)
plt.xlabel('预测标签', fontsize=12)
plt.ylabel('真实标签', fontsize=12)
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.savefig('混淆矩阵热力图.png', dpi=300, bbox_inches='tight')
plt.show()

# 4. 绘制性能指标对比图
print("📊 生成性能指标对比图...")
metrics = ['准确率', '精确率', '召回率', 'F1分数']
values = [accuracy, precision, recall, f1]

plt.figure(figsize=(10, 6))
bars = plt.bar(metrics, values, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
plt.title('模型性能指标', fontsize=16)
plt.ylabel('分数', fontsize=12)
plt.ylim(0, 1)

# 在柱状图上添加数值标签
for bar, value in zip(bars, values):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
             f'{value:.4f}', ha='center', va='bottom', fontsize=11)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('模型性能指标.png', dpi=300, bbox_inches='tight')
plt.show()

print("✅ 所有可视化图表已生成并保存！")
